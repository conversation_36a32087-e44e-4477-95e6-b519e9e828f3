# Weeks 4-7 Revised Roadmap: React-Flow Ready Backend

## 🎯 Goals Recap

**MVP (end of Week 7)**: LangGraph UI can search, open docs, see "related" recommendations, and (optionally) request raw network data.

**React-Flow network viz**: Not built now, but every backend contract, JSON shape, and performance index needed for a drop-in React-Flow component will be finished.

## Adjusted Roadmap

| Week  | Deliverable                                                                             | Notes / Changes                                                                  |
| ----- | --------------------------------------------------------------------------------------- | -------------------------------------------------------------------------------- |
| **4** | **v0 Hybrid `/search`** + stubbed `/recommend`                                         | No change from original plan.                                                   |
| **5** | **v1 `/search`** (+ practice-area filter & authority boost) & **JWT auth + rate-limit** | No change from original plan.                                                   |
| **6** | **Graph-data service** (React-Flow-ready) & nightly authority job                       | *Replace "demo page" with production-grade JSON contract & query optimizations.* |
| **7** | **Performance + caching + OpenAPI freeze**                                             | React-Flow UI can be added in "Phase 2" by FE without BE work.                 |

---

## Week 4: v0 Hybrid Search + Stubbed Recommendations

### 🎯 Objectives
- Implement hybrid search combining semantic and keyword search
- Create basic recommendation endpoint structure
- Establish API foundation for future enhancements

### 📋 Tasks

#### 1. Hybrid Search API (`/v0/search`)
- **Endpoint**: `GET /v0/search`
- **Parameters**:
  - `q` (string): Search query
  - `jurisdiction` (string, optional): Filter by jurisdiction (tx, oh, fed, etc.)
  - `doc_type` (string, optional): Filter by document type (case, statute)
  - `limit` (int, default=20): Number of results
  - `offset` (int, default=0): Pagination offset

- **Response Format**:
```json
{
  "results": [
    {
      "id": "C-2023-TX-123",
      "title": "Smith v. Jones",
      "type": "case",
      "jurisdiction": "tx",
      "authority_score": 0.87,
      "relevance_score": 0.92,
      "snippet": "...relevant text excerpt...",
      "metadata": {
        "court": "Texas Supreme Court",
        "date": "2023-03-15",
        "practice_areas": ["personal_injury", "negligence"]
      }
    }
  ],
  "total": 156,
  "query_time_ms": 245
}
```

#### 2. Search Implementation
- **Semantic Search**: Use Pinecone vector search with jurisdiction namespaces
- **Keyword Search**: Use Supabase full-text search
- **Hybrid Scoring**: Combine semantic similarity + keyword relevance
- **Authority Boost**: Apply Week 3 authority scores to ranking

#### 3. Stubbed Recommendations (`/v0/recommend`)
- **Endpoint**: `GET /v0/recommend/{document_id}`
- **Basic Implementation**: Return related documents from Neo4j relationships
- **Response Format**:
```json
{
  "recommendations": [
    {
      "id": "C-2019-TX-456",
      "title": "Johnson v. State",
      "type": "case",
      "relationship": "cites",
      "strength": 0.85,
      "reason": "Frequently cited together"
    }
  ]
}
```

### 🔧 Implementation Files
- `src/api/search/hybrid_search.py` - Main search logic
- `src/api/search/search_router.py` - FastAPI router
- `src/api/recommend/recommend_router.py` - Recommendation endpoints
- `tests/api/test_search_v0.py` - Test suite

---

## Week 5: Enhanced Search + Authentication

### 🎯 Objectives
- Enhance search with practice area filtering and authority boosting
- Implement JWT authentication and rate limiting
- Improve search performance and relevance

### 📋 Tasks

#### 1. Enhanced Search API (`/v1/search`)
- **Additional Parameters**:
  - `practice_areas` (array): Filter by practice areas
  - `authority_min` (float): Minimum authority score
  - `date_range` (object): Date filtering
  - `sort_by` (string): Sort options (relevance, authority, date)

#### 2. JWT Authentication
- **Supabase JWT Integration**: Verify JWT tokens from Supabase
- **Role-Based Access**: Different access levels (partner, attorney, paralegal, etc.)
- **Protected Endpoints**: Secure all API endpoints

#### 3. Rate Limiting
- **Implementation**: Use Redis or in-memory store
- **Limits**: 100 requests/minute per user, 1000/minute per tenant
- **Headers**: Include rate limit info in response headers

### 🔧 Implementation Files
- `src/api/auth/jwt_middleware.py` - JWT verification
- `src/api/auth/rate_limiter.py` - Rate limiting logic
- `src/api/search/enhanced_search.py` - v1 search implementation

---

## Week 6: Graph Data Service (React-Flow Ready)

### 🎯 Objectives
- Create production-grade graph data API for React-Flow
- Implement nightly authority score computation
- Optimize graph queries for performance

### 📋 Tasks

#### 1. Graph Data Contract (`/v0/graph`)

**Endpoint**: `GET /v0/graph`

**Parameters**:
- `id` (string): Document ID to center graph on
- `depth` (int, default=2): Relationship depth (1-3)
- `direction` (string, default="both"): Direction (both|in|out)
- `max_nodes` (int, default=50): Maximum nodes to return
- `node_types` (array, optional): Filter node types

**Response Format** (React-Flow Compatible):
```json
{
  "nodes": [
    {
      "id": "C-2023-TX-123",
      "label": "Smith v. Jones",
      "type": "case",
      "authority": 0.87,
      "data": {
        "jurisdiction": "tx",
        "court": "Texas Supreme Court",
        "date": "2023-03-15",
        "practice_areas": ["personal_injury"]
      }
    },
    {
      "id": "S-TX-CPRC-74",
      "label": "§74.001 CPRC",
      "type": "statute",
      "data": {
        "jurisdiction": "tx",
        "chapter": "74",
        "section": "001"
      }
    }
  ],
  "edges": [
    {
      "id": "edge-1",
      "source": "C-2023-TX-123",
      "target": "C-2019-TX-456",
      "type": "cites",
      "data": {
        "weight": 34,
        "relationship_type": "supportive"
      }
    },
    {
      "id": "edge-2",
      "source": "C-2023-TX-123",
      "target": "S-TX-CPRC-74",
      "type": "cites_statute",
      "data": {
        "citation_count": 12
      }
    }
  ],
  "metadata": {
    "center_node": "C-2023-TX-123",
    "total_nodes": 25,
    "total_edges": 48,
    "truncated": false,
    "query_time_ms": 156
  }
}
```

#### 2. Performance Requirements
- **Neo4j Indexes**: Create indexes on `:Case(id)` and `:Statute(id)`
- **Query Limits**: ≤ 200 nodes, ≤ 600 edges; return `"truncated": true` if exceeded
- **Response Time**: < 500ms for typical queries

#### 3. Cypher Query Implementation
```cypher
MATCH (start {id: $id})-[:CITES*1..$depth]-(n)
WITH DISTINCT start, n
OPTIONAL MATCH (start)-[r]-(n)
RETURN start, n, collect(r) AS relationships
LIMIT $max_nodes
```

#### 4. Nightly Authority Job
- **Schedule**: Run daily at 2 AM
- **Algorithm**: PageRank-based authority calculation
- **Storage**: Update authority scores in Supabase and Neo4j
- **Monitoring**: Log job status and performance metrics

### 🔧 Implementation Files
- `src/api/graph/graph_service.py` - Graph data service
- `src/api/graph/graph_router.py` - FastAPI router
- `src/jobs/authority_calculator.py` - Nightly authority job
- `docs/graph_api.md` - API documentation

---

## Week 7: Performance + Caching + OpenAPI Freeze

### 🎯 Objectives
- Implement Redis caching for all endpoints
- Achieve p95 latency targets
- Freeze OpenAPI specification for frontend handoff

### 📋 Tasks

#### 1. Redis Caching
- **Search Cache**: Cache search results for 15 minutes
- **Graph Cache**: Cache graph data for 1 hour
- **Recommendation Cache**: Cache recommendations for 30 minutes
- **Cache Keys**: Include user permissions in cache keys

#### 2. Performance Targets
- **Search API**: p95 < 800ms
- **Graph API**: p95 < 500ms
- **Recommendation API**: p95 < 300ms

#### 3. OpenAPI Documentation
- **Complete Specification**: All endpoints documented
- **Examples**: Include request/response examples
- **Authentication**: Document JWT requirements
- **Rate Limits**: Document all rate limiting

#### 4. Frontend Handoff Assets
- **Sample Data**: `/examples/react_flow_sample.json` (30-node sample)
- **OpenAPI Spec**: `/openapi.yaml` (frozen specification)
- **Documentation**: `docs/graph_api.md` with React-Flow integration hints

### 🔧 Implementation Files
- `src/cache/redis_cache.py` - Redis caching layer
- `src/api/performance/monitoring.py` - Performance monitoring
- `openapi.yaml` - Complete API specification
- `examples/react_flow_sample.json` - Sample graph data

---

## 🔧 Technical Implementation Details

### Database Indexes (Week 6)
```sql
-- Neo4j Indexes
CREATE INDEX case_id_index FOR (c:Case) ON (c.id);
CREATE INDEX statute_id_index FOR (s:Statute) ON (s.id);
CREATE INDEX document_authority_index FOR (d:Document) ON (d.authority_score);

-- Supabase Indexes
CREATE INDEX idx_documents_authority ON documents(authority_score DESC);
CREATE INDEX idx_documents_practice_areas ON documents USING GIN(practice_areas);
```

### Cache Strategy (Week 7)
```python
# Cache key patterns
SEARCH_CACHE_KEY = "search:{query_hash}:{user_permissions_hash}"
GRAPH_CACHE_KEY = "graph:{doc_id}:{depth}:{direction}:{user_permissions_hash}"
RECOMMEND_CACHE_KEY = "recommend:{doc_id}:{user_permissions_hash}"

# TTL values
SEARCH_TTL = 900  # 15 minutes
GRAPH_TTL = 3600  # 1 hour
RECOMMEND_TTL = 1800  # 30 minutes
```

---

## 🎯 Success Criteria

### Week 4
- [ ] Hybrid search API returns relevant results
- [ ] Basic recommendation endpoint functional
- [ ] API response times < 1 second

### Week 5
- [ ] JWT authentication working
- [ ] Rate limiting implemented
- [ ] Practice area filtering functional

### Week 6
- [ ] Graph API returns React-Flow compatible JSON
- [ ] Neo4j queries optimized with proper indexes
- [ ] Nightly authority job running successfully

### Week 7
- [ ] All APIs cached with Redis
- [ ] Performance targets met (p95 < 800ms)
- [ ] OpenAPI specification complete and frozen
- [ ] Frontend handoff assets delivered

---

## 🚀 Post-MVP Handoff (Phase 2)

### Deliverables for Frontend Team
1. **`/examples/react_flow_sample.json`** - 30-node sample graph
2. **`/openapi.yaml`** - Frozen API specification
3. **`docs/graph_api.md`** - Graph API documentation with React-Flow hints

### Frontend Integration
With these assets, the Frontend team can:
- Drop in a React-Flow component at any time
- Use the sample JSON for development and testing
- Implement the full graph visualization without backend changes

---

## ❓ Open Questions (Require Response ≤ 24h)

1. **Graph Size Limits**: Confirm max graph size limits (current: 200 nodes, 600 edges)
2. **Cache Strategy**: Confirm Redis vs. in-process LRU for `/graph` cache
3. **JWT Configuration**: Provide Supabase JWKS URL for JWT verification
4. **Authority Algorithm**: Confirm PageRank parameters for authority calculation

---

## 🎬 Next Actions

1. **Create Week 4 branch**: `git checkout -b feature/week4-hybrid-search`
2. **Migrate open issues**: Update GitHub issues with revised roadmap
3. **Start Week 4 implementation**: Begin with hybrid search API
4. **Set up monitoring**: Implement performance tracking from Week 4
