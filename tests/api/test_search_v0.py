"""
Test suite for Week 4 search API implementation.
Tests hybrid search functionality, caching, authentication, and error handling.
"""

import pytest
import json
from unittest.mock import Mock, patch, MagicMock
from fastapi.testclient import TestClient

from src.api.main import app
from src.api.search.hybrid_search import HybridSearchEngine, SearchResult, SearchResponse
from src.cache.cache import cache


@pytest.fixture
def client():
    """Test client fixture."""
    return TestClient(app)


@pytest.fixture
def mock_user():
    """Mock authenticated user fixture."""
    return {
        "user_id": "test_user_123",
        "email": "<EMAIL>",
        "role": "attorney",
        "permissions": ["search", "recommend"],
        "tenant_id": "test_tenant",
        "jurisdictions": ["tx", "oh"],
        "permissions_hash": "test_hash_123"
    }


@pytest.fixture
def mock_search_results():
    """Mock search results fixture."""
    return SearchResponse(
        results=[
            SearchResult(
                id="C-2023-TX-123",
                title="<PERSON> v. Jones Medical Malpractice",
                type="case",
                jurisdiction="tx",
                authority_score=0.87,
                relevance_score=0.92,
                snippet="This case involves medical malpractice claims...",
                metadata={
                    "court": "Texas Supreme Court",
                    "date": "2023-03-15",
                    "practice_areas": ["personal_injury", "medical_malpractice"]
                }
            ),
            SearchResult(
                id="S-TX-CPRC-74",
                title="§74.001 CPRC - Medical Liability",
                type="statute",
                jurisdiction="tx",
                authority_score=0.92,
                relevance_score=0.88,
                snippet="Medical liability statute defining standards...",
                metadata={
                    "chapter": "74",
                    "section": "001",
                    "effective_date": "2003-09-01"
                }
            )
        ],
        total=2,
        query_time_ms=245,
        query="medical malpractice",
        filters_applied={
            "jurisdiction": "tx",
            "doc_type": None,
            "limit": 20,
            "offset": 0
        }
    )


class TestSearchAPI:
    """Test cases for search API endpoints."""
    
    @patch('src.api.search.search_router.get_current_user')
    @patch('src.api.search.search_router.search_engine')
    def test_search_basic_functionality(self, mock_search_engine, mock_auth, client, mock_user, mock_search_results):
        """Test basic search functionality."""
        # Setup mocks
        mock_auth.return_value = mock_user
        mock_search_engine.search.return_value = mock_search_results
        
        # Make request
        response = client.get("/v0/search?q=medical malpractice&jurisdiction=tx")
        
        # Assertions
        assert response.status_code == 200
        data = response.json()
        
        assert data["query"] == "medical malpractice"
        assert data["total"] == 2
        assert len(data["results"]) == 2
        assert data["query_time_ms"] == 245
        assert data["cached"] == False
        
        # Check first result
        first_result = data["results"][0]
        assert first_result["id"] == "C-2023-TX-123"
        assert first_result["title"] == "Smith v. Jones Medical Malpractice"
        assert first_result["type"] == "case"
        assert first_result["jurisdiction"] == "tx"
        assert first_result["authority_score"] == 0.87
        assert first_result["relevance_score"] == 0.92
    
    @patch('src.api.search.search_router.get_current_user')
    def test_search_authentication_required(self, mock_auth, client):
        """Test that authentication is required."""
        mock_auth.side_effect = Exception("Authentication required")
        
        response = client.get("/v0/search?q=test")
        assert response.status_code == 500  # Will be handled by exception handler
    
    @patch('src.api.search.search_router.get_current_user')
    def test_search_jurisdiction_access_control(self, mock_auth, client, mock_user):
        """Test jurisdiction-based access control."""
        mock_auth.return_value = mock_user
        
        # Request access to unauthorized jurisdiction
        response = client.get("/v0/search?q=test&jurisdiction=ca")
        assert response.status_code == 403
        
        data = response.json()
        assert "Access denied to jurisdiction" in data["message"]
    
    @patch('src.api.search.search_router.get_current_user')
    @patch('src.api.search.search_router.search_engine')
    def test_search_parameter_validation(self, mock_search_engine, mock_auth, client, mock_user):
        """Test search parameter validation."""
        mock_auth.return_value = mock_user
        
        # Test empty query
        response = client.get("/v0/search?q=")
        assert response.status_code == 422  # Validation error
        
        # Test invalid limit
        response = client.get("/v0/search?q=test&limit=0")
        assert response.status_code == 422
        
        # Test invalid offset
        response = client.get("/v0/search?q=test&offset=-1")
        assert response.status_code == 422
        
        # Test limit too high
        response = client.get("/v0/search?q=test&limit=1000")
        assert response.status_code == 422
    
    @patch('src.api.search.search_router.get_current_user')
    @patch('src.cache.cache.cache')
    def test_search_caching(self, mock_cache, mock_auth, client, mock_user, mock_search_results):
        """Test search result caching."""
        mock_auth.return_value = mock_user
        
        # Setup cache mock
        cached_response = {
            "results": [],
            "total": 0,
            "query_time_ms": 50,
            "query": "cached query",
            "filters_applied": {},
            "cached": True
        }
        mock_cache.get.return_value = cached_response
        
        response = client.get("/v0/search?q=cached query")
        
        # Should return cached result
        assert response.status_code == 200
        data = response.json()
        assert data["cached"] == True
        assert data["query_time_ms"] == 50
    
    @patch('src.api.search.search_router.get_current_user')
    @patch('src.api.search.search_router.search_engine')
    def test_search_post_endpoint(self, mock_search_engine, mock_auth, client, mock_user, mock_search_results):
        """Test POST search endpoint."""
        mock_auth.return_value = mock_user
        mock_search_engine.search.return_value = mock_search_results
        
        # Make POST request
        request_data = {
            "q": "medical malpractice",
            "jurisdiction": "tx",
            "doc_type": "case",
            "limit": 10,
            "offset": 0
        }
        
        response = client.post("/v0/search", json=request_data)
        
        assert response.status_code == 200
        data = response.json()
        assert data["query"] == "medical malpractice"
        assert data["total"] == 2
    
    @patch('src.api.search.search_router.get_current_user')
    def test_search_suggestions(self, mock_auth, client, mock_user):
        """Test search suggestions endpoint."""
        mock_auth.return_value = mock_user
        
        response = client.get("/v0/search/suggestions?q=neg")
        
        assert response.status_code == 200
        data = response.json()
        assert "suggestions" in data
        assert "negligence" in data["suggestions"]
        assert data["query"] == "neg"
    
    @patch('src.api.search.search_router.get_current_user')
    def test_search_stats(self, mock_auth, client, mock_user):
        """Test search stats endpoint."""
        mock_auth.return_value = mock_user
        
        response = client.get("/v0/search/stats")
        
        assert response.status_code == 200
        data = response.json()
        assert data["search_engine_status"] == "operational"
        assert data["semantic_search_enabled"] == True
        assert data["keyword_search_enabled"] == True
        assert "supported_jurisdictions" in data
        assert "tx" in data["supported_jurisdictions"]
    
    def test_search_health_check(self, client):
        """Test search health check endpoint."""
        response = client.get("/v0/search/health")
        
        assert response.status_code == 200
        data = response.json()
        assert "status" in data
        assert "dependencies" in data


class TestHybridSearchEngine:
    """Test cases for the hybrid search engine."""
    
    @patch('src.api.search.hybrid_search.PineconeConnector')
    @patch('src.api.search.hybrid_search.SupabaseConnector')
    @patch('src.api.search.hybrid_search.Neo4jConnector')
    def test_search_engine_initialization(self, mock_neo4j, mock_supabase, mock_pinecone):
        """Test search engine initialization."""
        engine = HybridSearchEngine()
        
        assert engine.semantic_weight == 0.6
        assert engine.keyword_weight == 0.4
        assert engine.authority_boost == 0.2
    
    @patch('src.api.search.hybrid_search.PineconeConnector')
    @patch('src.api.search.hybrid_search.SupabaseConnector')
    @patch('src.api.search.hybrid_search.Neo4jConnector')
    def test_semantic_search(self, mock_neo4j, mock_supabase, mock_pinecone):
        """Test semantic search functionality."""
        # Setup mocks
        mock_pinecone_instance = Mock()
        mock_pinecone.return_value = mock_pinecone_instance
        mock_pinecone_instance.query_embeddings.return_value = [
            {
                "id": "test_doc_1",
                "score": 0.95,
                "metadata": {"title": "Test Document 1"}
            }
        ]
        
        engine = HybridSearchEngine()
        results = engine._semantic_search("test query", "tx", "case", 10)
        
        assert len(results) == 1
        assert results[0][0] == "test_doc_1"
        assert results[0][1] == 0.95
    
    @patch('src.api.search.hybrid_search.PineconeConnector')
    @patch('src.api.search.hybrid_search.SupabaseConnector')
    @patch('src.api.search.hybrid_search.Neo4jConnector')
    def test_keyword_search(self, mock_neo4j, mock_supabase, mock_pinecone):
        """Test keyword search functionality."""
        # Setup mocks
        mock_supabase_instance = Mock()
        mock_supabase.return_value = mock_supabase_instance
        
        mock_table = Mock()
        mock_supabase_instance.table.return_value = mock_table
        mock_table.select.return_value = mock_table
        mock_table.text_search.return_value = mock_table
        mock_table.eq.return_value = mock_table
        mock_table.limit.return_value = mock_table
        mock_table.execute.return_value = Mock(data=[
            {
                "document_id": "test_doc_1",
                "title": "Test Document 1",
                "doc_type": "case",
                "jurisdiction": "tx",
                "authority_score": 0.8,
                "content": "This is test content with the query term"
            }
        ])
        
        engine = HybridSearchEngine()
        results = engine._keyword_search("test query", "tx", "case", 10)
        
        assert len(results) == 1
        assert results[0][0] == "test_doc_1"
        assert results[0][1] > 0  # Should have some relevance score
    
    def test_keyword_relevance_calculation(self):
        """Test keyword relevance score calculation."""
        engine = HybridSearchEngine()
        
        # Test with matching content
        content = "This document discusses medical malpractice cases in detail"
        relevance = engine._calculate_keyword_relevance("medical malpractice", content)
        assert relevance > 0
        
        # Test with no matching content
        relevance = engine._calculate_keyword_relevance("medical malpractice", "unrelated content")
        assert relevance == 0
    
    def test_snippet_generation(self):
        """Test snippet generation with query highlighting."""
        engine = HybridSearchEngine()
        
        content = "This is a long document about medical malpractice. It contains many details about the case and the legal implications."
        snippet = engine._generate_snippet(content, "medical malpractice", max_length=50)
        
        assert len(snippet) <= 53  # 50 + "..."
        assert "medical malpractice" in snippet.lower()


@pytest.fixture(autouse=True)
def clear_cache():
    """Clear cache before each test."""
    try:
        cache.clear()
    except:
        pass  # Ignore cache clear errors in tests
