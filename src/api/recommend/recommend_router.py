"""
FastAPI router for recommendation endpoints (Week 4).
Implements v0 recommendation API with basic Neo4j relationship-based recommendations.
"""

import logging
from typing import List, Optional
from fastapi import APIRouter, Depends, HTTPException, Query, Path
from pydantic import BaseModel, Field

from src.processing.storage.neo4j_connector import Neo4jConnector
from src.api.auth.jwt_middleware import get_current_user
from src.cache.cache import cache

logger = logging.getLogger(__name__)

# Create router
router = APIRouter(prefix="/v0", tags=["recommendations"])

# Initialize Neo4j connector
neo4j = Neo4jConnector()


class RecommendationItem(BaseModel):
    """Individual recommendation item."""
    id: str
    title: str
    type: str
    jurisdiction: str
    relationship: str
    strength: float = Field(..., ge=0.0, le=1.0, description="Relationship strength score")
    reason: str
    authority_score: Optional[float] = Field(None, ge=0.0, le=1.0)
    metadata: dict = {}


class RecommendationResponse(BaseModel):
    """Complete recommendation response."""
    document_id: str
    recommendations: List[RecommendationItem]
    total: int
    query_time_ms: int
    cached: bool = False


class RecommendationService:
    """Service for generating document recommendations."""
    
    def __init__(self):
        """Initialize recommendation service."""
        self.neo4j = Neo4jConnector()
    
    def get_recommendations(self, 
                          doc_id: str, 
                          limit: int = 10,
                          user_jurisdictions: List[str] = None) -> RecommendationResponse:
        """
        Get recommendations for a document based on Neo4j relationships.
        
        For Week 4, this implements basic relationship-based recommendations:
        1. Documents that cite the same cases/statutes
        2. Documents cited by the target document
        3. Documents that cite the target document
        4. Documents with similar practice areas
        """
        import time
        start_time = time.time()
        
        try:
            recommendations = []
            
            # Get direct citations (outgoing)
            outgoing_citations = self._get_outgoing_citations(doc_id, limit // 4)
            recommendations.extend(outgoing_citations)
            
            # Get reverse citations (incoming)
            incoming_citations = self._get_incoming_citations(doc_id, limit // 4)
            recommendations.extend(incoming_citations)
            
            # Get co-cited documents (documents that cite the same targets)
            co_cited = self._get_co_cited_documents(doc_id, limit // 4)
            recommendations.extend(co_cited)
            
            # Get similar practice area documents
            similar_practice = self._get_similar_practice_area_documents(doc_id, limit // 4)
            recommendations.extend(similar_practice)
            
            # Filter by user jurisdictions if provided
            if user_jurisdictions:
                recommendations = [
                    rec for rec in recommendations 
                    if rec.jurisdiction in user_jurisdictions
                ]
            
            # Remove duplicates and sort by strength
            seen_ids = set()
            unique_recommendations = []
            for rec in recommendations:
                if rec.id not in seen_ids and rec.id != doc_id:
                    seen_ids.add(rec.id)
                    unique_recommendations.append(rec)
            
            # Sort by strength and limit
            unique_recommendations.sort(key=lambda x: x.strength, reverse=True)
            final_recommendations = unique_recommendations[:limit]
            
            query_time_ms = int((time.time() - start_time) * 1000)
            
            return RecommendationResponse(
                document_id=doc_id,
                recommendations=final_recommendations,
                total=len(final_recommendations),
                query_time_ms=query_time_ms,
                cached=False
            )
            
        except Exception as e:
            logger.error(f"Error generating recommendations for {doc_id}: {e}")
            return RecommendationResponse(
                document_id=doc_id,
                recommendations=[],
                total=0,
                query_time_ms=int((time.time() - start_time) * 1000),
                cached=False
            )
    
    def _get_outgoing_citations(self, doc_id: str, limit: int) -> List[RecommendationItem]:
        """Get documents cited by the target document."""
        try:
            query = """
            MATCH (source:Document {document_id: $doc_id})-[r:CITES]->(target:Document)
            RETURN target.document_id AS id,
                   target.title AS title,
                   target.doc_type AS type,
                   target.jurisdiction AS jurisdiction,
                   COALESCE(target.authority_score, 0.0) AS authority_score,
                   COALESCE(r.citation_count, 1) AS citation_count
            ORDER BY authority_score DESC, citation_count DESC
            LIMIT $limit
            """
            
            with self.neo4j.driver.session() as session:
                result = session.run(query, doc_id=doc_id, limit=limit)
                
                recommendations = []
                for record in result:
                    rec = RecommendationItem(
                        id=record["id"],
                        title=record["title"] or "Unknown Document",
                        type=record["type"] or "unknown",
                        jurisdiction=record["jurisdiction"] or "unknown",
                        relationship="cites",
                        strength=min(1.0, record["authority_score"] + 0.1),
                        reason="Cited by this document",
                        authority_score=record["authority_score"],
                        metadata={"citation_count": record["citation_count"]}
                    )
                    recommendations.append(rec)
                
                return recommendations
                
        except Exception as e:
            logger.error(f"Error getting outgoing citations: {e}")
            return []
    
    def _get_incoming_citations(self, doc_id: str, limit: int) -> List[RecommendationItem]:
        """Get documents that cite the target document."""
        try:
            query = """
            MATCH (source:Document)-[r:CITES]->(target:Document {document_id: $doc_id})
            RETURN source.document_id AS id,
                   source.title AS title,
                   source.doc_type AS type,
                   source.jurisdiction AS jurisdiction,
                   COALESCE(source.authority_score, 0.0) AS authority_score,
                   COALESCE(r.citation_count, 1) AS citation_count
            ORDER BY authority_score DESC, citation_count DESC
            LIMIT $limit
            """
            
            with self.neo4j.driver.session() as session:
                result = session.run(query, doc_id=doc_id, limit=limit)
                
                recommendations = []
                for record in result:
                    rec = RecommendationItem(
                        id=record["id"],
                        title=record["title"] or "Unknown Document",
                        type=record["type"] or "unknown",
                        jurisdiction=record["jurisdiction"] or "unknown",
                        relationship="cited_by",
                        strength=min(1.0, record["authority_score"] + 0.05),
                        reason="Cites this document",
                        authority_score=record["authority_score"],
                        metadata={"citation_count": record["citation_count"]}
                    )
                    recommendations.append(rec)
                
                return recommendations
                
        except Exception as e:
            logger.error(f"Error getting incoming citations: {e}")
            return []
    
    def _get_co_cited_documents(self, doc_id: str, limit: int) -> List[RecommendationItem]:
        """Get documents that cite the same targets as the source document."""
        try:
            query = """
            MATCH (source:Document {document_id: $doc_id})-[:CITES]->(common:Document)<-[:CITES]-(related:Document)
            WHERE related.document_id <> $doc_id
            WITH related, COUNT(common) AS shared_citations
            RETURN related.document_id AS id,
                   related.title AS title,
                   related.doc_type AS type,
                   related.jurisdiction AS jurisdiction,
                   COALESCE(related.authority_score, 0.0) AS authority_score,
                   shared_citations
            ORDER BY shared_citations DESC, authority_score DESC
            LIMIT $limit
            """
            
            with self.neo4j.driver.session() as session:
                result = session.run(query, doc_id=doc_id, limit=limit)
                
                recommendations = []
                for record in result:
                    # Calculate strength based on shared citations
                    strength = min(1.0, (record["shared_citations"] * 0.1) + record["authority_score"])
                    
                    rec = RecommendationItem(
                        id=record["id"],
                        title=record["title"] or "Unknown Document",
                        type=record["type"] or "unknown",
                        jurisdiction=record["jurisdiction"] or "unknown",
                        relationship="co_cited",
                        strength=strength,
                        reason=f"Shares {record['shared_citations']} common citations",
                        authority_score=record["authority_score"],
                        metadata={"shared_citations": record["shared_citations"]}
                    )
                    recommendations.append(rec)
                
                return recommendations
                
        except Exception as e:
            logger.error(f"Error getting co-cited documents: {e}")
            return []
    
    def _get_similar_practice_area_documents(self, doc_id: str, limit: int) -> List[RecommendationItem]:
        """Get documents with similar practice areas."""
        try:
            query = """
            MATCH (source:Document {document_id: $doc_id})
            MATCH (related:Document)
            WHERE related.document_id <> $doc_id
              AND any(pa IN source.practice_areas WHERE pa IN related.practice_areas)
            RETURN related.document_id AS id,
                   related.title AS title,
                   related.doc_type AS type,
                   related.jurisdiction AS jurisdiction,
                   COALESCE(related.authority_score, 0.0) AS authority_score,
                   related.practice_areas AS practice_areas
            ORDER BY authority_score DESC
            LIMIT $limit
            """
            
            with self.neo4j.driver.session() as session:
                result = session.run(query, doc_id=doc_id, limit=limit)
                
                recommendations = []
                for record in result:
                    rec = RecommendationItem(
                        id=record["id"],
                        title=record["title"] or "Unknown Document",
                        type=record["type"] or "unknown",
                        jurisdiction=record["jurisdiction"] or "unknown",
                        relationship="similar_practice_area",
                        strength=record["authority_score"] * 0.8,  # Lower strength for practice area similarity
                        reason="Similar practice area",
                        authority_score=record["authority_score"],
                        metadata={"practice_areas": record["practice_areas"]}
                    )
                    recommendations.append(rec)
                
                return recommendations
                
        except Exception as e:
            logger.error(f"Error getting similar practice area documents: {e}")
            return []


# Initialize recommendation service
recommendation_service = RecommendationService()


@router.get("/recommend/{document_id}", response_model=RecommendationResponse)
async def get_document_recommendations(
    document_id: str = Path(..., description="Document ID to get recommendations for"),
    limit: int = Query(10, description="Number of recommendations to return", ge=1, le=50),
    user: dict = Depends(get_current_user)
) -> RecommendationResponse:
    """
    Get recommendations for a specific document.
    
    Returns related documents based on:
    - Citation relationships (both directions)
    - Co-citation patterns
    - Similar practice areas
    - Authority scores
    
    **Authentication**: Requires valid JWT token
    **Rate Limits**: 100 requests/minute per user
    """
    try:
        logger.info(f"Recommendation request: user={user['user_id']}, doc_id={document_id}")
        
        # Check cache first
        cache_key = cache.recommend_cache_key(
            doc_id=document_id,
            user_permissions_hash=user["permissions_hash"]
        )
        
        cached_result = cache.get(cache_key)
        if cached_result:
            logger.debug(f"Returning cached recommendations for document: {document_id}")
            cached_result["cached"] = True
            return RecommendationResponse(**cached_result)
        
        # Get user's allowed jurisdictions
        user_jurisdictions = user.get("jurisdictions", [])
        
        # Generate recommendations
        response = recommendation_service.get_recommendations(
            doc_id=document_id,
            limit=limit,
            user_jurisdictions=user_jurisdictions
        )
        
        # Cache the result
        cache.set(cache_key, response.dict(), cache.RECOMMEND_TTL)
        
        logger.info(f"Recommendations generated: {response.total} items in {response.query_time_ms}ms")
        return response
        
    except Exception as e:
        logger.error(f"Recommendation error: {e}", exc_info=True)
        raise HTTPException(
            status_code=500,
            detail="Error generating recommendations"
        )


@router.get("/recommend/{document_id}/similar")
async def get_similar_documents(
    document_id: str = Path(..., description="Document ID to find similar documents for"),
    limit: int = Query(5, description="Number of similar documents to return", ge=1, le=20),
    user: dict = Depends(get_current_user)
) -> dict:
    """
    Get documents similar to the specified document.
    
    This endpoint focuses specifically on content and practice area similarity,
    separate from citation-based recommendations.
    """
    try:
        # For Week 4, use the similar practice area logic
        similar_docs = recommendation_service._get_similar_practice_area_documents(
            doc_id=document_id,
            limit=limit
        )
        
        return {
            "document_id": document_id,
            "similar_documents": [doc.dict() for doc in similar_docs],
            "total": len(similar_docs),
            "similarity_type": "practice_area"
        }
        
    except Exception as e:
        logger.error(f"Similar documents error: {e}", exc_info=True)
        raise HTTPException(
            status_code=500,
            detail="Error finding similar documents"
        )
