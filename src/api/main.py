"""
Main FastAPI application for Week 4 implementation.
Combines search, recommendation, and graph APIs with authentication and rate limiting.
"""

import logging
import time
from contextlib import asynccontextmanager
from typing import Dict, Any

from fastapi import FastAP<PERSON>, Request, HTTPException, Depends
from fastapi.middleware.cors import CORSMiddleware
from fastapi.middleware.trustedhost import TrustedHostMiddleware
from fastapi.responses import JSONResponse
import uvicorn

from src.api.search.search_router import router as search_router
from src.api.recommend.recommend_router import router as recommend_router
from src.api.graph.graph_router import router as graph_router
from src.api.auth.jwt_middleware import get_current_user
from src.cache.cache import cache

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


# Rate limiting storage (in-memory for Week 4, Redis in production)
rate_limit_storage: Dict[str, Dict[str, Any]] = {}


class RateLimiter:
    """Simple rate limiter for API endpoints."""
    
    def __init__(self, requests_per_minute: int = 100):
        self.requests_per_minute = requests_per_minute
        self.window_size = 60  # 1 minute in seconds
    
    def is_allowed(self, user_id: str) -> bool:
        """Check if user is within rate limits."""
        current_time = time.time()
        
        if user_id not in rate_limit_storage:
            rate_limit_storage[user_id] = {
                "requests": [],
                "last_cleanup": current_time
            }
        
        user_data = rate_limit_storage[user_id]
        
        # Clean up old requests (older than window_size)
        cutoff_time = current_time - self.window_size
        user_data["requests"] = [
            req_time for req_time in user_data["requests"] 
            if req_time > cutoff_time
        ]
        
        # Check if under limit
        if len(user_data["requests"]) >= self.requests_per_minute:
            return False
        
        # Add current request
        user_data["requests"].append(current_time)
        return True
    
    def get_remaining_requests(self, user_id: str) -> int:
        """Get remaining requests for user."""
        if user_id not in rate_limit_storage:
            return self.requests_per_minute
        
        current_requests = len(rate_limit_storage[user_id]["requests"])
        return max(0, self.requests_per_minute - current_requests)


# Initialize rate limiter
rate_limiter = RateLimiter(requests_per_minute=100)


@asynccontextmanager
async def lifespan(app: FastAPI):
    """Application lifespan manager."""
    # Startup
    logger.info("Starting Legal Database API v0.1.0")
    logger.info(f"Cache backend: {cache.backend.__class__.__name__}")
    
    # Test database connections
    try:
        # Test cache
        cache.set("startup_test", "ok", 60)
        if cache.get("startup_test") == "ok":
            logger.info("Cache connection: OK")
        else:
            logger.warning("Cache connection: Failed")
        
        logger.info("API startup completed successfully")
    except Exception as e:
        logger.error(f"Startup error: {e}")
    
    yield
    
    # Shutdown
    logger.info("Shutting down Legal Database API")


# Create FastAPI app
app = FastAPI(
    title="Legal Database API",
    description="React-Flow ready backend for legal document search, recommendations, and network visualization",
    version="0.1.0",
    docs_url="/docs",
    redoc_url="/redoc",
    lifespan=lifespan
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Configure appropriately for production
    allow_credentials=True,
    allow_methods=["GET", "POST", "PUT", "DELETE"],
    allow_headers=["*"],
)

# Add trusted host middleware for production
app.add_middleware(
    TrustedHostMiddleware,
    allowed_hosts=["*"]  # Configure appropriately for production
)


@app.middleware("http")
async def rate_limit_middleware(request: Request, call_next):
    """Rate limiting middleware."""
    # Skip rate limiting for health checks and docs
    if request.url.path in ["/health", "/docs", "/redoc", "/openapi.json"]:
        response = await call_next(request)
        return response
    
    # Skip rate limiting for non-API endpoints
    if not request.url.path.startswith("/v0/"):
        response = await call_next(request)
        return response
    
    try:
        # Extract user from JWT token
        auth_header = request.headers.get("authorization")
        if not auth_header or not auth_header.startswith("Bearer "):
            # Allow unauthenticated requests to proceed (will be caught by auth middleware)
            response = await call_next(request)
            return response
        
        # For rate limiting, we'll use a simple approach
        # In production, this would integrate with the JWT middleware
        user_id = "anonymous"  # Placeholder for Week 4
        
        # Check rate limit
        if not rate_limiter.is_allowed(user_id):
            remaining = rate_limiter.get_remaining_requests(user_id)
            return JSONResponse(
                status_code=429,
                content={
                    "error": "rate_limit_exceeded",
                    "message": "Rate limit exceeded. Try again later.",
                    "remaining_requests": remaining,
                    "reset_time": 60
                },
                headers={
                    "X-RateLimit-Limit": str(rate_limiter.requests_per_minute),
                    "X-RateLimit-Remaining": str(remaining),
                    "X-RateLimit-Reset": str(int(time.time()) + 60)
                }
            )
        
        # Process request
        response = await call_next(request)
        
        # Add rate limit headers
        remaining = rate_limiter.get_remaining_requests(user_id)
        response.headers["X-RateLimit-Limit"] = str(rate_limiter.requests_per_minute)
        response.headers["X-RateLimit-Remaining"] = str(remaining)
        response.headers["X-RateLimit-Reset"] = str(int(time.time()) + 60)
        
        return response
        
    except Exception as e:
        logger.error(f"Rate limiting error: {e}")
        # Continue without rate limiting on error
        response = await call_next(request)
        return response


@app.middleware("http")
async def logging_middleware(request: Request, call_next):
    """Request logging middleware."""
    start_time = time.time()
    
    # Process request
    response = await call_next(request)
    
    # Log request
    process_time = time.time() - start_time
    logger.info(
        f"{request.method} {request.url.path} - "
        f"Status: {response.status_code} - "
        f"Time: {process_time:.3f}s"
    )
    
    # Add timing header
    response.headers["X-Process-Time"] = str(process_time)
    
    return response


# Include routers
app.include_router(search_router)
app.include_router(recommend_router)
app.include_router(graph_router)


@app.get("/")
async def root():
    """Root endpoint with API information."""
    return {
        "name": "Legal Database API",
        "version": "0.1.0",
        "description": "React-Flow ready backend for legal document search and network visualization",
        "endpoints": {
            "search": "/v0/search",
            "recommendations": "/v0/recommend/{document_id}",
            "graph": "/v0/graph",
            "health": "/health",
            "docs": "/docs"
        },
        "features": [
            "Hybrid search (semantic + keyword)",
            "Citation-based recommendations",
            "React-Flow compatible graph data",
            "JWT authentication",
            "Rate limiting",
            "Response caching"
        ]
    }


@app.get("/health")
async def health_check():
    """Comprehensive health check endpoint."""
    try:
        health_status = {
            "status": "healthy",
            "timestamp": time.time(),
            "version": "0.1.0",
            "services": {
                "api": "healthy",
                "cache": "unknown",
                "database": "unknown"
            },
            "metrics": {
                "uptime_seconds": time.time(),  # Would be actual uptime
                "cache_backend": cache.backend.__class__.__name__,
                "rate_limit_storage_size": len(rate_limit_storage)
            }
        }
        
        # Test cache
        try:
            cache.set("health_check", "ok", 60)
            if cache.get("health_check") == "ok":
                health_status["services"]["cache"] = "healthy"
            else:
                health_status["services"]["cache"] = "unhealthy"
        except Exception as e:
            health_status["services"]["cache"] = "unhealthy"
            logger.error(f"Cache health check failed: {e}")
        
        # Overall status
        unhealthy_services = [
            service for service, status in health_status["services"].items()
            if status == "unhealthy"
        ]
        
        if unhealthy_services:
            health_status["status"] = "degraded"
            health_status["unhealthy_services"] = unhealthy_services
        
        return health_status
        
    except Exception as e:
        logger.error(f"Health check error: {e}")
        return {
            "status": "unhealthy",
            "error": str(e),
            "timestamp": time.time()
        }


@app.get("/metrics")
async def get_metrics():
    """Basic metrics endpoint for monitoring."""
    return {
        "api_version": "0.1.0",
        "cache_backend": cache.backend.__class__.__name__,
        "rate_limit_users": len(rate_limit_storage),
        "total_requests_tracked": sum(
            len(user_data["requests"]) 
            for user_data in rate_limit_storage.values()
        ),
        "timestamp": time.time()
    }


@app.exception_handler(HTTPException)
async def http_exception_handler(request: Request, exc: HTTPException):
    """Custom HTTP exception handler."""
    return JSONResponse(
        status_code=exc.status_code,
        content={
            "error": "http_error",
            "message": exc.detail,
            "status_code": exc.status_code,
            "path": request.url.path,
            "timestamp": time.time()
        }
    )


@app.exception_handler(Exception)
async def general_exception_handler(request: Request, exc: Exception):
    """General exception handler for unhandled errors."""
    logger.error(f"Unhandled exception: {exc}", exc_info=True)
    return JSONResponse(
        status_code=500,
        content={
            "error": "internal_server_error",
            "message": "An internal error occurred",
            "path": request.url.path,
            "timestamp": time.time()
        }
    )


if __name__ == "__main__":
    # Development server
    uvicorn.run(
        "src.api.main:app",
        host="0.0.0.0",
        port=8000,
        reload=True,
        log_level="info"
    )
