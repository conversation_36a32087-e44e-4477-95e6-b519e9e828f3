"""
FastAPI router for hybrid search endpoints (Week 4).
Implements v0 search API with semantic + keyword search combination.
"""

import logging
from typing import Optional, List
from fastapi import APIRouter, Depends, HTTPException, Query
from pydantic import BaseModel, Field

from src.api.search.hybrid_search import HybridSearchEngine, SearchResponse
from src.api.auth.jwt_middleware import get_current_user
from src.cache.cache import cache

logger = logging.getLogger(__name__)

# Create router
router = APIRouter(prefix="/v0", tags=["search"])

# Initialize search engine
search_engine = HybridSearchEngine()


class SearchRequest(BaseModel):
    """Search request model for validation."""
    q: str = Field(..., description="Search query", min_length=1, max_length=500)
    jurisdiction: Optional[str] = Field(None, description="Filter by jurisdiction (tx, oh, fed, etc.)")
    doc_type: Optional[str] = Field(None, description="Filter by document type (case, statute)")
    limit: int = Field(20, description="Number of results to return", ge=1, le=100)
    offset: int = Field(0, description="Pagination offset", ge=0)


class SearchResultResponse(BaseModel):
    """Individual search result response model."""
    id: str
    title: str
    type: str
    jurisdiction: str
    authority_score: float
    relevance_score: float
    snippet: str
    metadata: dict


class SearchAPIResponse(BaseModel):
    """Complete search API response model."""
    results: List[SearchResultResponse]
    total: int
    query_time_ms: int
    query: str
    filters_applied: dict
    cached: bool = False


@router.get("/search", response_model=SearchAPIResponse)
async def search_documents(
    q: str = Query(..., description="Search query", min_length=1, max_length=500),
    jurisdiction: Optional[str] = Query(None, description="Filter by jurisdiction (tx, oh, fed, etc.)"),
    doc_type: Optional[str] = Query(None, description="Filter by document type (case, statute)"),
    limit: int = Query(20, description="Number of results to return", ge=1, le=100),
    offset: int = Query(0, description="Pagination offset", ge=0),
    user: dict = Depends(get_current_user)
) -> SearchAPIResponse:
    """
    Hybrid search endpoint combining semantic and keyword search.
    
    This endpoint:
    1. Performs semantic search using Pinecone embeddings
    2. Performs keyword search using Supabase full-text search
    3. Combines results using hybrid scoring
    4. Applies authority score boosting
    5. Returns paginated results with metadata
    
    **Authentication**: Requires valid JWT token
    **Rate Limits**: 100 requests/minute per user
    """
    try:
        logger.info(f"Search request: user={user['user_id']}, query='{q}', jurisdiction={jurisdiction}")
        
        # Validate jurisdiction access
        user_jurisdictions = user.get("jurisdictions", [])
        if jurisdiction and jurisdiction not in user_jurisdictions:
            raise HTTPException(
                status_code=403,
                detail=f"Access denied to jurisdiction: {jurisdiction}"
            )
        
        # Check cache first
        cache_key = cache.search_cache_key(
            query=q,
            filters={
                "jurisdiction": jurisdiction,
                "doc_type": doc_type,
                "limit": limit,
                "offset": offset
            },
            user_permissions_hash=user["permissions_hash"]
        )
        
        cached_result = cache.get(cache_key)
        if cached_result:
            logger.debug(f"Returning cached search results for query: '{q}'")
            cached_result["cached"] = True
            return SearchAPIResponse(**cached_result)
        
        # Perform search
        search_response = search_engine.search(
            query=q,
            jurisdiction=jurisdiction,
            doc_type=doc_type,
            limit=limit,
            offset=offset
        )
        
        # Convert to API response format
        api_results = []
        for result in search_response.results:
            api_result = SearchResultResponse(
                id=result.id,
                title=result.title,
                type=result.type,
                jurisdiction=result.jurisdiction,
                authority_score=result.authority_score,
                relevance_score=result.relevance_score,
                snippet=result.snippet,
                metadata=result.metadata
            )
            api_results.append(api_result)
        
        api_response = SearchAPIResponse(
            results=api_results,
            total=search_response.total,
            query_time_ms=search_response.query_time_ms,
            query=search_response.query,
            filters_applied=search_response.filters_applied,
            cached=False
        )
        
        # Cache the result
        cache.set(cache_key, api_response.dict(), cache.SEARCH_TTL)
        
        logger.info(f"Search completed: {len(api_results)} results in {search_response.query_time_ms}ms")
        return api_response
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Search error: {e}", exc_info=True)
        raise HTTPException(
            status_code=500,
            detail="Internal search error"
        )


@router.post("/search", response_model=SearchAPIResponse)
async def search_documents_post(
    request: SearchRequest,
    user: dict = Depends(get_current_user)
) -> SearchAPIResponse:
    """
    POST version of search endpoint for complex queries.
    
    Supports the same functionality as GET /search but allows for
    more complex request bodies and longer queries.
    """
    return await search_documents(
        q=request.q,
        jurisdiction=request.jurisdiction,
        doc_type=request.doc_type,
        limit=request.limit,
        offset=request.offset,
        user=user
    )


@router.get("/search/suggestions")
async def get_search_suggestions(
    q: str = Query(..., description="Partial query for suggestions", min_length=1, max_length=100),
    limit: int = Query(5, description="Number of suggestions", ge=1, le=10),
    user: dict = Depends(get_current_user)
) -> dict:
    """
    Get search query suggestions based on partial input.
    
    This endpoint provides autocomplete suggestions for search queries
    based on popular searches and document titles.
    """
    try:
        # For Week 4, return simple suggestions based on common legal terms
        # This can be enhanced in later weeks with ML-based suggestions
        
        common_terms = [
            "negligence", "medical malpractice", "personal injury", "premises liability",
            "product liability", "wrongful death", "motor vehicle accident", "slip and fall",
            "breach of contract", "employment law", "discrimination", "harassment",
            "workers compensation", "insurance bad faith", "defamation", "privacy"
        ]
        
        # Filter terms that start with the query
        suggestions = [term for term in common_terms if term.lower().startswith(q.lower())]
        suggestions = suggestions[:limit]
        
        return {
            "suggestions": suggestions,
            "query": q,
            "total": len(suggestions)
        }
        
    except Exception as e:
        logger.error(f"Suggestions error: {e}", exc_info=True)
        raise HTTPException(
            status_code=500,
            detail="Error generating suggestions"
        )


@router.get("/search/stats")
async def get_search_stats(
    user: dict = Depends(get_current_user)
) -> dict:
    """
    Get search statistics and system status.
    
    Returns information about search performance, cache hit rates,
    and system health for monitoring purposes.
    """
    try:
        # Basic stats for Week 4 - can be enhanced with real metrics
        stats = {
            "search_engine_status": "operational",
            "semantic_search_enabled": True,
            "keyword_search_enabled": True,
            "cache_backend": cache.backend.__class__.__name__,
            "supported_jurisdictions": ["tx", "oh", "fed", "ny", "fl"],
            "supported_document_types": ["case", "statute"],
            "version": "v0.1.0"
        }
        
        return stats
        
    except Exception as e:
        logger.error(f"Stats error: {e}", exc_info=True)
        raise HTTPException(
            status_code=500,
            detail="Error retrieving search stats"
        )


# Health check endpoint
@router.get("/search/health")
async def search_health_check():
    """
    Health check endpoint for search service.
    
    Returns the health status of search dependencies:
    - Pinecone connection
    - Supabase connection
    - Cache backend
    """
    try:
        health_status = {
            "status": "healthy",
            "timestamp": cache.backend.__class__.__name__,
            "dependencies": {
                "pinecone": "unknown",  # Will be checked in implementation
                "supabase": "unknown",  # Will be checked in implementation
                "cache": "healthy" if cache.get("health_check") is not None or cache.set("health_check", "ok") else "unhealthy"
            }
        }
        
        return health_status
        
    except Exception as e:
        logger.error(f"Health check error: {e}", exc_info=True)
        return {
            "status": "unhealthy",
            "error": str(e)
        }
